# Data directories
/histdata/ASCII
/histdata/MT

# Cache files (Phase 5.4: Smart Disk Cache System)
# Ignore cache data files but keep directory structure and Python files
streamtrade/cache/data/m1_base/*.parquet
streamtrade/cache/data/timeframes/*.parquet
streamtrade/cache/data/indicators/*.parquet
streamtrade/cache/metadata/index.json
streamtrade/cache/metadata/lru_tracker.json
streamtrade/cache/styles/*.json
*.cache
streamtrade/config/user_settings.json

# Python cache
__marimo__
streamtrade/visualization/__pycache__
streamtrade/gui/components/__pycache__
streamtrade/gui/__pycache__
streamtrade/indicators/__pycache__
streamtrade/data/__pycache__
streamtrade/core/__pycache__
streamtrade/config/__pycache__
streamtrade/__pycache__
streamtrade/tests/__pycache__
streamtrade/indicators/custom/__pycache__
streamtrade/cache/__pycache__

# Logs
streamtrade/logs/*.log

# IDE and OS
.vscode/
.idea/
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*.swo