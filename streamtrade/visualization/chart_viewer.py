"""
Chart viewer for StreamTrade platform.
Integrates data management, indicators, and visualization.
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import plotly.graph_objects as go

from ..data.enhanced_data_manager import EnhancedDataManager
from ..indicators.indicator_manager import IndicatorManager
from .plotly_charts import PlotlyCharts
from ..config.logging_config import get_logger
from ..config.settings import settings

logger = get_logger(__name__)


class ChartViewer:
    """
    Main chart viewer that integrates data, indicators, and visualization.
    
    Features:
    - Load and display OHLCV data
    - Apply multiple technical indicators
    - Interactive chart with zoom/pan
    - Dynamic timeframe switching
    - Indicator management
    - Export capabilities
    """
    
    def __init__(self):
        self.data_manager = EnhancedDataManager()
        self.indicator_manager = IndicatorManager()
        self.plotly_charts = PlotlyCharts()

        # Current chart state
        self.current_data = None
        self.current_pair = None
        self.current_timeframe = None
        self.current_indicators = {}

        logger.info("<PERSON><PERSON>iewer initialized with EnhancedDataManager")
    
    def load_data(
        self,
        pair: str,
        timeframe: str = "H1",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        max_candles: Optional[int] = None
    ) -> bool:
        """
        Load data for chart display.
        
        Args:
            pair: Currency pair
            timeframe: Timeframe
            start_date: Start date (optional)
            end_date: End date (optional)
            max_candles: Maximum number of candles
            
        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading data: {pair} {timeframe}")
            
            # Use default max_candles if not specified
            if max_candles is None:
                max_candles = settings.chart_settings["max_candles_display"]
            
            # Load data using enhanced data manager
            if start_date and end_date:
                data = self.data_manager.load_data_range(pair, timeframe, start_date, end_date)
            elif max_candles:
                # Convert max_candles to days_back for compatibility
                days_back = max(1, max_candles // 24)  # Rough conversion
                data = self.data_manager.load_n_days_back(pair, timeframe, days_back)
            else:
                # Default to 5 days back
                data = self.data_manager.load_n_days_back(pair, timeframe, 5)
            
            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe}")
                return False
            
            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe
            
            # Clear previous indicator results
            self.current_indicators = {}
            
            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False

    def load_data_n_days_back(self, pair: str, timeframe: str, days_back: int) -> bool:
        """
        Load data using N Days Back strategy.

        Args:
            pair: Currency pair
            timeframe: Timeframe
            days_back: Number of days to load back

        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading {days_back} days back: {pair} {timeframe}")

            data = self.data_manager.load_n_days_back(pair, timeframe, days_back)

            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe} ({days_back} days back)")
                return False

            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe

            # Clear previous indicator results
            self.current_indicators = {}

            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe} ({days_back} days back)")
            return True

        except Exception as e:
            logger.error(f"Error loading data N days back: {str(e)}")
            return False

    def load_data_range(self, pair: str, timeframe: str, start_date: datetime, end_date: datetime) -> bool:
        """
        Load data for specific date range.

        Args:
            pair: Currency pair
            timeframe: Timeframe
            start_date: Start date
            end_date: End date

        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading date range: {pair} {timeframe} ({start_date.date()} to {end_date.date()})")

            data = self.data_manager.load_data_range(pair, timeframe, start_date, end_date)

            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe} in date range")
                return False

            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe

            # Clear previous indicator results
            self.current_indicators = {}

            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe} (date range)")
            return True

        except Exception as e:
            logger.error(f"Error loading data range: {str(e)}")
            return False

    def load_all_available_data(self, pair: str, timeframe: str) -> bool:
        """
        Load all available data for pair and timeframe.

        Args:
            pair: Currency pair
            timeframe: Timeframe

        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading all available data: {pair} {timeframe}")

            # Use a large days_back value to get all available data
            data = self.data_manager.load_n_days_back(pair, timeframe, 365)  # 1 year max

            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe}")
                return False

            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe

            # Clear previous indicator results
            self.current_indicators = {}

            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe} (all available)")
            return True

        except Exception as e:
            logger.error(f"Error loading all available data: {str(e)}")
            return False
    
    def add_indicator(
        self,
        name: str,
        indicator_type: str,
        parameters: Optional[Dict[str, Any]] = None,
        display_name: Optional[str] = None
    ) -> bool:
        """
        Add an indicator to the chart.
        
        Args:
            name: Unique name for indicator instance
            indicator_type: Type of indicator
            parameters: Indicator parameters
            display_name: Display name for UI
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            success = self.indicator_manager.add_indicator(
                name=name,
                indicator_type=indicator_type,
                parameters=parameters,
                display_name=display_name
            )
            
            if success:
                logger.info(f"Added indicator: {name} ({indicator_type})")
                # Recalculate indicators if data is available
                if self.current_data is not None:
                    self._calculate_indicators()
            
            return success
            
        except Exception as e:
            logger.error(f"Error adding indicator: {str(e)}")
            return False
    
    def remove_indicator(self, name: str) -> bool:
        """Remove an indicator from the chart."""
        try:
            success = self.indicator_manager.remove_indicator(name)
            
            if success:
                # Remove from current indicators
                if name in self.current_indicators:
                    del self.current_indicators[name]
                
                logger.info(f"Removed indicator: {name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error removing indicator: {str(e)}")
            return False
    
    def update_indicator_parameters(
        self,
        name: str,
        parameters: Dict[str, Any]
    ) -> bool:
        """Update parameters for an existing indicator."""
        try:
            success = self.indicator_manager.update_indicator_parameters(name, parameters)
            
            if success and self.current_data is not None:
                # Recalculate indicators
                self._calculate_indicators()
                logger.info(f"Updated indicator parameters: {name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating indicator parameters: {str(e)}")
            return False
    
    def toggle_indicator(self, name: str, enabled: bool) -> bool:
        """Enable or disable an indicator."""
        try:
            if enabled:
                success = self.indicator_manager.enable_indicator(name)
            else:
                success = self.indicator_manager.disable_indicator(name)
            
            if success and self.current_data is not None:
                self._calculate_indicators()
            
            return success
            
        except Exception as e:
            logger.error(f"Error toggling indicator: {str(e)}")
            return False
    
    def _calculate_indicators(self):
        """Calculate all indicators for current data."""
        try:
            if self.current_data is None:
                return
            
            self.current_indicators = self.indicator_manager.calculate_all(self.current_data)
            logger.debug(f"Calculated {len(self.current_indicators)} indicators")
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {str(e)}")
    
    def create_chart(
        self,
        title: Optional[str] = None,
        height: Optional[int] = None,
        width: Optional[int] = None,
        remove_gaps: bool = True,
        chart_style: str = "Candlestick",
        show_vertical_line: bool = True,
        show_horizontal_line: bool = True
    ) -> go.Figure:
        """
        Create chart with current data and indicators.

        Args:
            title: Chart title (auto-generated if None)
            height: Chart height
            width: Chart width
            remove_gaps: Whether to remove weekend gaps
            chart_style: Chart style (Candlestick, OHLC Bars, Line)
            show_vertical_line: Show vertical crosshair line
            show_horizontal_line: Show horizontal crosshair line

        Returns:
            Plotly Figure object
        """
        try:
            if self.current_data is None:
                logger.warning("No data loaded for chart creation")
                return self._create_empty_chart()
            
            # Generate title if not provided
            if title is None:
                title = f"{self.current_pair} - {self.current_timeframe}"
                if len(self.current_data) > 0:
                    start_date = self.current_data.index[0].strftime("%Y-%m-%d")
                    end_date = self.current_data.index[-1].strftime("%Y-%m-%d")
                    title += f" ({start_date} to {end_date})"
            
            # Calculate indicators if not already done
            if not self.current_indicators and len(self.indicator_manager.indicators) > 0:
                self._calculate_indicators()
            
            # Create chart
            fig = self.plotly_charts.create_candlestick_chart(
                data=self.current_data,
                indicators=self.current_indicators,
                title=title,
                height=height,
                width=width,
                remove_gaps=remove_gaps,
                chart_style=chart_style,
                show_vertical_line=show_vertical_line,
                show_horizontal_line=show_horizontal_line
            )
            
            logger.debug(f"Created chart with {len(self.current_data)} candles and {len(self.current_indicators)} indicators")
            return fig
            
        except Exception as e:
            logger.error(f"Error creating chart: {str(e)}")
            return self._create_error_chart(str(e))
    
    def refresh_chart(self) -> go.Figure:
        """Refresh chart with current settings."""
        try:
            if self.current_pair and self.current_timeframe:
                # Reload data
                self.load_data(self.current_pair, self.current_timeframe)
                
                # Create new chart
                return self.create_chart()
            else:
                logger.warning("No current data to refresh")
                return self._create_empty_chart()
                
        except Exception as e:
            logger.error(f"Error refreshing chart: {str(e)}")
            return self._create_error_chart(str(e))
    
    def change_timeframe(self, new_timeframe: str) -> go.Figure:
        """
        Change timeframe and update chart (optimized for speed).

        Args:
            new_timeframe: New timeframe to display

        Returns:
            Updated chart figure
        """
        try:
            if not self.current_pair:
                logger.warning("No pair loaded for timeframe change")
                return self._create_empty_chart()

            logger.info(f"Switching timeframe: {self.current_timeframe} → {new_timeframe}")

            # Store current indicators configuration for re-application
            current_indicators_config = {}
            if self.current_indicators:
                for name, result in self.current_indicators.items():
                    indicator_info = self.indicator_manager.get_indicator_info(name)
                    if indicator_info:
                        current_indicators_config[name] = {
                            'type': indicator_info['name'],
                            'parameters': indicator_info['current_parameters'],
                            'enabled': indicator_info['enabled']
                        }

            # Use optimized timeframe switching
            data = self.data_manager.switch_timeframe(
                pair=self.current_pair,
                new_timeframe=new_timeframe
            )

            if data is None or data.empty:
                logger.error(f"No data available for {self.current_pair} {new_timeframe}")
                return self._create_error_chart(f"No data available for {new_timeframe}")

            # Update current state
            self.current_data = data
            self.current_timeframe = new_timeframe

            # Clear previous indicator results
            self.current_indicators = {}

            # Re-apply indicators if they existed
            if current_indicators_config:
                logger.debug(f"Re-applying {len(current_indicators_config)} indicators")
                self._calculate_indicators()

            # Create chart with new timeframe data
            logger.info(f"Successfully switched to {new_timeframe}: {len(data)} candles")
            return self.create_chart()

        except Exception as e:
            logger.error(f"Error changing timeframe: {str(e)}")
            return self._create_error_chart(str(e))

    def get_data_info(self) -> Dict[str, Any]:
        """Get information about current data and user context."""
        info = {
            'pair': self.current_pair,
            'timeframe': self.current_timeframe,
            'candles_loaded': len(self.current_data) if self.current_data is not None else 0,
            'user_context': None
        }

        if self.current_pair:
            user_context = self.data_manager.get_user_context(self.current_pair)
            if user_context:
                info['user_context'] = {
                    'last_timeframe': user_context.get('last_timeframe'),
                    'last_days_back': user_context.get('last_days_back')
                }

        return info

    def get_chart_info(self) -> Dict[str, Any]:
        """Get information about current chart state."""
        return {
            'pair': self.current_pair,
            'timeframe': self.current_timeframe,
            'data_points': len(self.current_data) if self.current_data is not None else 0,
            'date_range': {
                'start': self.current_data.index[0] if self.current_data is not None and len(self.current_data) > 0 else None,
                'end': self.current_data.index[-1] if self.current_data is not None and len(self.current_data) > 0 else None
            },
            'indicators': list(self.current_indicators.keys()),
            'indicator_count': len(self.current_indicators)
        }
    
    def get_available_pairs(self) -> List[str]:
        """Get list of available currency pairs."""
        return self.data_manager.get_available_pairs()

    def get_available_timeframes(self) -> List[str]:
        """Get list of available timeframes."""
        return self.data_manager.get_available_timeframes()
    
    def get_available_indicators(self) -> Dict[str, str]:
        """Get list of available indicators."""
        return self.indicator_manager.get_available_indicators()
    
    def get_indicators_by_category(self) -> Dict[str, List[str]]:
        """Get indicators grouped by category."""
        return self.indicator_manager.get_indicators_by_category()
    
    def get_indicator_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific indicator."""
        return self.indicator_manager.get_indicator_info(name)
    
    def export_indicator_config(self) -> str:
        """Export current indicator configuration."""
        return self.indicator_manager.export_configuration()
    
    def import_indicator_config(self, config_json: str) -> bool:
        """Import indicator configuration."""
        success = self.indicator_manager.import_configuration(config_json)
        
        if success and self.current_data is not None:
            self._calculate_indicators()
        
        return success
    
    def _create_empty_chart(self) -> go.Figure:
        """Create empty chart placeholder."""
        fig = go.Figure()
        
        fig.add_annotation(
            text="No data loaded<br>Please select a currency pair and timeframe",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        
        fig.update_layout(
            title="StreamTrade Chart",
            height=600,
            width=1000,
            template=settings.chart_settings["theme"]
        )
        
        return fig
    
    def _create_error_chart(self, error_message: str) -> go.Figure:
        """Create error chart."""
        return self.plotly_charts._create_error_chart(error_message)
