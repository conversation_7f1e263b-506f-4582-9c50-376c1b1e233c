# Smart Disk Cache System

This directory contains the Smart Disk Cache System implementation for the Lionaire platform.

## Directory Structure

```
streamtrade/cache/
├── __init__.py                 # Cache module initialization
├── cache_metadata.py           # Cache metadata management
├── disk_cache.py              # Smart Disk Cache core
├── indicator_cache.py         # Indicator caching system
├── lru_manager.py             # LRU eviction manager
├── README.md                  # This file
├── data/                      # Cache data storage (ignored by git)
│   ├── m1_base/              # M1 base data (*.parquet files)
│   ├── timeframes/           # Converted timeframe data (*.parquet files)
│   └── indicators/           # Per-indicator calculations (*.parquet files)
├── metadata/                 # Cache metadata (ignored by git)
│   ├── index.json           # Cache index and metadata
│   └── lru_tracker.json     # LRU eviction tracking
└── styles/                   # Indicator styles (ignored by git)
    └── *.json               # Style configuration files
```

## Cache Files (Ignored by Git)

The following files are automatically generated and managed by the cache system:

- **Data Files**: `*.parquet` files in `data/` subdirectories
- **Metadata Files**: `index.json`, `lru_tracker.json` in `metadata/`
- **Style Files**: `*.json` files in `styles/`
- **Python Cache**: `__pycache__/` directories

These files are ignored by git to prevent committing cache data while preserving the code structure.

## Usage

The cache system is automatically initialized when the Enhanced Data Manager is created:

```python
from streamtrade.data.enhanced_data_manager import EnhancedDataManager

# Cache system is automatically initialized
data_manager = EnhancedDataManager()

# Cache is used transparently for data loading
data = data_manager.load_n_days_back('EURUSD', 'H1', 5)
```

## Configuration

Cache behavior can be configured through user settings:

```json
{
  "cache": {
    "max_cache_size_gb": 10,
    "enable_disk_cache": true,
    "cache_compression": "snappy",
    "insufficient_data_behavior": "show_warning"
  }
}
```

## Features

- **Persistent Storage**: Cache survives application restarts
- **LRU Eviction**: Automatic cleanup of old data
- **Compression**: Efficient Parquet storage with compression
- **Thread Safety**: Concurrent access support
- **Error Recovery**: Graceful handling of corrupted cache
- **Dependency Tracking**: Cascade deletion for related data

For detailed documentation, see:
- `streamtrade/docs/015-phase5-4-smart-disk-cache.md`
- `streamtrade/docs/016-phase5-4-completion-summary.md`
