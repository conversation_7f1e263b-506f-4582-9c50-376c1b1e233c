"""
Enhanced Data Manager for Lionaire Platform.
Implements N Days Back loading strategy and session-aware timeframe conversion.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime, timedelta
import threading
from pathlib import Path

from ..config.settings import settings
from ..config.logging_config import get_logger, log_performance
from ..config.user_settings import get_user_settings
from ..core.utils import memory_usage, estimate_memory_usage
from .data_loader import DataLoader
from .session_aware_converter import SessionAwareConverter

logger = get_logger(__name__)


class EnhancedDataManager:
    """
    Enhanced data management system with session-aware timeframe conversion.
    
    Features:
    - N Days Back loading strategy (replaces "Last N Candles")
    - Session-aware timeframe conversion
    - Intelligent M1 base caching
    - Memory management with user-configurable limits
    - Thread-safe operations
    """
    
    def __init__(self):
        self.data_loader = DataLoader()
        self.session_converter = SessionAwareConverter()
        self.user_settings = get_user_settings()
        
        # Cache settings from user preferences
        cache_settings = self.user_settings.get_cache_settings()
        data_settings = self.user_settings.get_data_loading_settings()
        
        self.max_cache_size_mb = cache_settings.get('max_cache_size_gb', 10) * 1024  # Convert GB to MB
        self.max_candles_load = data_settings.get('max_candles_load', 200000)
        self.max_candles_display = data_settings.get('max_candles_display', 15000)
        
        # Data cache: {cache_key: {'data': DataFrame, 'timestamp': datetime, 'size_mb': float}}
        self._data_cache = {}
        self._cache_lock = threading.Lock()
        
        # M1 base data cache for efficient timeframe switching
        self._m1_base_cache = {}  # {pair: {'data': DataFrame, 'timestamp': datetime, 'size_mb': float}}
        
        # User request context for preserving data range across timeframe switches
        self._user_context = {}  # {pair: {'days_back': int, 'start_date': datetime, 'end_date': datetime}}
        
        logger.info("EnhancedDataManager initialized with session-aware conversion")
    
    def load_n_days_back(self, pair: str, timeframe: str, days_back: int = None) -> Optional[pd.DataFrame]:
        """
        Load data based on N days back instead of candle count.
        
        Args:
            pair: Currency pair symbol
            timeframe: Target timeframe
            days_back: Number of days to load back (uses user setting if None)
            
        Returns:
            DataFrame with requested data or None if error
        """
        try:
            # Use user setting if days_back not specified
            if days_back is None:
                days_back = self.user_settings.get('data_loading.days_back_default', 5)
            
            # Validate minimum days
            min_days = self.user_settings.get('data_loading.days_back_minimum', 1)
            days_back = max(days_back, min_days)
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            logger.info(f"Loading {days_back} days back for {pair} ({timeframe}): {start_date.date()} to {end_date.date()}")
            
            # Store user context for timeframe switching
            self._user_context[pair] = {
                'days_back': days_back,
                'start_date': start_date,
                'end_date': end_date
            }
            
            return self.load_data_range(pair, timeframe, start_date, end_date)
            
        except Exception as e:
            logger.error(f"Error loading {days_back} days back for {pair}: {e}")
            return None
    
    @log_performance
    def load_data_range(self, pair: str, timeframe: str, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Load data for specific date range with session-aware conversion.
        
        Args:
            pair: Currency pair symbol
            timeframe: Target timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            DataFrame with requested data or None if error
        """
        try:
            # Check if timeframe is enabled
            if not self.user_settings.is_timeframe_enabled(timeframe):
                logger.warning(f"Timeframe {timeframe} is disabled in user settings")
                return None
            
            # Generate cache key
            cache_key = f"{pair}_{timeframe}_{start_date.date()}_{end_date.date()}"
            
            # Check cache first
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                logger.debug(f"Using cached data for {cache_key}")
                return self._limit_display_candles(cached_data)
            
            # Load M1 base data
            m1_data = self._load_m1_base_data(pair, start_date, end_date)
            if m1_data is None or m1_data.empty:
                logger.warning(f"No M1 data available for {pair} in range {start_date.date()} to {end_date.date()}")
                return None
            
            # Convert to target timeframe if needed
            if timeframe == 'M1':
                result_data = m1_data
            else:
                result_data = self.session_converter.convert_timeframe(m1_data, timeframe, pair)
                if result_data is None:
                    logger.error(f"Failed to convert {pair} to {timeframe}")
                    return None
            
            # Cache the result
            self._add_to_cache(cache_key, result_data)
            
            # Apply display limit
            limited_data = self._limit_display_candles(result_data)
            
            logger.info(f"Loaded {len(result_data)} {timeframe} candles for {pair} (displaying {len(limited_data)})")
            return limited_data
            
        except Exception as e:
            logger.error(f"Error loading data range for {pair}: {e}")
            return None
    
    def _load_m1_base_data(self, pair: str, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Load M1 base data with intelligent caching.
        
        Args:
            pair: Currency pair symbol
            start_date: Start date
            end_date: End date
            
        Returns:
            M1 DataFrame or None if error
        """
        try:
            # Check M1 cache first
            m1_cache_key = f"{pair}_M1"
            
            with self._cache_lock:
                if m1_cache_key in self._m1_base_cache:
                    cached_m1 = self._m1_base_cache[m1_cache_key]
                    cached_data = cached_m1['data']
                    
                    # Check if cached data covers the requested range
                    if (not cached_data.empty and 
                        cached_data.index[0] <= start_date and 
                        cached_data.index[-1] >= end_date):
                        
                        # Filter to requested range
                        mask = (cached_data.index >= start_date) & (cached_data.index <= end_date)
                        filtered_data = cached_data[mask]
                        
                        logger.debug(f"Using cached M1 data for {pair}")
                        return filtered_data
            
            # Load fresh M1 data from files
            logger.info(f"Loading fresh M1 data for {pair}")
            m1_data = self.data_loader.load_data_range(pair, start_date, end_date, max_candles=self.max_candles_load)
            
            if m1_data is not None and not m1_data.empty:
                # Cache M1 data for future use
                self._cache_m1_base_data(pair, m1_data)
                
                # Filter to requested range
                mask = (m1_data.index >= start_date) & (m1_data.index <= end_date)
                filtered_data = m1_data[mask]
                
                return filtered_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error loading M1 base data for {pair}: {e}")
            return None
    
    def _cache_m1_base_data(self, pair: str, m1_data: pd.DataFrame):
        """Cache M1 base data for efficient timeframe switching."""
        try:
            cache_key = f"{pair}_M1"
            data_size_mb = estimate_memory_usage(m1_data)
            
            with self._cache_lock:
                self._m1_base_cache[cache_key] = {
                    'data': m1_data.copy(),
                    'timestamp': datetime.now(),
                    'size_mb': data_size_mb
                }
                
                # Check cache size and cleanup if needed
                self._cleanup_cache_if_needed()
                
            logger.debug(f"Cached {len(m1_data)} M1 candles for {pair} ({data_size_mb:.1f} MB)")
            
        except Exception as e:
            logger.error(f"Error caching M1 data for {pair}: {e}")
    
    def switch_timeframe(self, pair: str, new_timeframe: str) -> Optional[pd.DataFrame]:
        """
        Switch timeframe using cached M1 data and preserved user context.
        
        Args:
            pair: Currency pair symbol
            new_timeframe: Target timeframe
            
        Returns:
            DataFrame with new timeframe data or None if error
        """
        try:
            # Check if timeframe is enabled
            if not self.user_settings.is_timeframe_enabled(new_timeframe):
                logger.warning(f"Timeframe {new_timeframe} is disabled in user settings")
                return None
            
            # Get user context for this pair
            if pair not in self._user_context:
                logger.warning(f"No user context found for {pair}, using default days back")
                return self.load_n_days_back(pair, new_timeframe)
            
            context = self._user_context[pair]
            start_date = context['start_date']
            end_date = context['end_date']
            
            logger.info(f"Switching {pair} to {new_timeframe} using cached data range")
            
            return self.load_data_range(pair, new_timeframe, start_date, end_date)
            
        except Exception as e:
            logger.error(f"Error switching timeframe for {pair}: {e}")
            return None
    
    def _limit_display_candles(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Limit displayed candles based on user settings.
        
        Args:
            data: Input DataFrame
            
        Returns:
            Limited DataFrame showing most recent candles
        """
        if data.empty:
            return data
        
        if len(data) <= self.max_candles_display:
            return data
        
        # Show most recent candles
        limited_data = data.tail(self.max_candles_display)
        
        logger.debug(f"Limited display from {len(data)} to {len(limited_data)} candles")
        return limited_data
    
    def _get_from_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """Get data from cache if available and valid."""
        with self._cache_lock:
            if cache_key in self._data_cache:
                cached_item = self._data_cache[cache_key]
                
                # Check if cache is still valid (within timeout)
                cache_age = datetime.now() - cached_item['timestamp']
                cache_timeout = timedelta(minutes=30)  # 30 minutes timeout
                
                if cache_age < cache_timeout:
                    return cached_item['data'].copy()
                else:
                    # Remove expired cache
                    del self._data_cache[cache_key]
        
        return None
    
    def _add_to_cache(self, cache_key: str, data: pd.DataFrame):
        """Add data to cache."""
        try:
            data_size_mb = estimate_memory_usage(data)
            
            with self._cache_lock:
                self._data_cache[cache_key] = {
                    'data': data.copy(),
                    'timestamp': datetime.now(),
                    'size_mb': data_size_mb
                }
                
                # Check cache size and cleanup if needed
                self._cleanup_cache_if_needed()
                
        except Exception as e:
            logger.error(f"Error adding to cache: {e}")
    
    def _cleanup_cache_if_needed(self):
        """Cleanup cache if it exceeds size limits."""
        try:
            # Calculate total cache size
            total_size_mb = sum(item['size_mb'] for item in self._data_cache.values())
            total_size_mb += sum(item['size_mb'] for item in self._m1_base_cache.values())
            
            if total_size_mb > self.max_cache_size_mb:
                logger.info(f"Cache size ({total_size_mb:.1f} MB) exceeds limit ({self.max_cache_size_mb:.1f} MB), cleaning up")
                
                # Remove oldest entries from data cache
                if self._data_cache:
                    sorted_items = sorted(self._data_cache.items(), key=lambda x: x[1]['timestamp'])
                    items_to_remove = len(sorted_items) // 4  # Remove 25% of items
                    
                    for i in range(items_to_remove):
                        cache_key = sorted_items[i][0]
                        del self._data_cache[cache_key]
                
                logger.info(f"Cache cleanup completed")
                
        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")
    
    def clear_cache(self):
        """Clear all caches."""
        with self._cache_lock:
            self._data_cache.clear()
            self._m1_base_cache.clear()
            self._user_context.clear()
        
        # Clear session converter cache
        self.session_converter.clear_cache()
        
        logger.info("All caches cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information and statistics."""
        with self._cache_lock:
            data_cache_size = sum(item['size_mb'] for item in self._data_cache.values())
            m1_cache_size = sum(item['size_mb'] for item in self._m1_base_cache.values())
            total_cache_size = data_cache_size + m1_cache_size
            
            return {
                'data_cache_entries': len(self._data_cache),
                'data_cache_size_mb': data_cache_size,
                'm1_cache_entries': len(self._m1_base_cache),
                'm1_cache_size_mb': m1_cache_size,
                'total_cache_size_mb': total_cache_size,
                'max_cache_size_mb': self.max_cache_size_mb,
                'cache_utilization_percent': (total_cache_size / self.max_cache_size_mb) * 100,
                'user_contexts': len(self._user_context),
                'session_converter_cache': self.session_converter.get_cache_info()
            }
    
    def get_insufficient_data_behavior(self) -> str:
        """Get user preference for insufficient data handling."""
        return self.user_settings.get('cache.insufficient_data_behavior', 'show_warning')
    
    def update_user_settings(self):
        """Update internal settings from user preferences."""
        cache_settings = self.user_settings.get_cache_settings()
        data_settings = self.user_settings.get_data_loading_settings()
        
        self.max_cache_size_mb = cache_settings.get('max_cache_size_gb', 10) * 1024
        self.max_candles_load = data_settings.get('max_candles_load', 200000)
        self.max_candles_display = data_settings.get('max_candles_display', 15000)
        
        logger.info("User settings updated in EnhancedDataManager")
