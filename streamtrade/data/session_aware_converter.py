"""
Session-Aware Timeframe Converter for Lionaire Platform.
Converts M1 data to other timeframes using proper market session boundaries.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime, timedelta, time
import threading

from ..config.logging_config import get_logger, log_performance
from ..config.user_settings import get_user_settings
from ..core.utils import validate_dataframe

logger = get_logger(__name__)


class SessionAwareConverter:
    """
    Session-aware timeframe converter that respects market boundaries.
    
    Features:
    - Forex vs Non-Forex market session boundaries
    - Gap-aware conversion (handles missing M1 data)
    - Proper H4/D1/W1 candle formation
    - Timezone-aware calculations
    - Variable session length handling
    """
    
    def __init__(self):
        self.user_settings = get_user_settings()
        self.cache = {}
        self._lock = threading.Lock()
        
        # Supported timeframes
        self.supported_timeframes = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN1']
        
        logger.info("SessionAwareConverter initialized")
    
    def get_session_boundaries(self, pair: str, date: datetime) -> Dict[str, Any]:
        """
        Get market session boundaries for specific pair and date.
        
        Args:
            pair: Currency pair symbol
            date: Target date
            
        Returns:
            Dictionary with session information
        """
        is_forex = self.user_settings.is_forex_pair(pair)
        market_open_str = self.user_settings.get_market_open_time(pair)
        
        # Parse market open time
        market_open_hour, market_open_minute = map(int, market_open_str.split(':'))
        market_open_time = time(market_open_hour, market_open_minute)
        
        # Calculate session boundaries for the date
        session_start = datetime.combine(date.date(), market_open_time)
        
        # H4 session boundaries
        h4_sessions = []
        current_time = session_start
        
        for i in range(6):  # Maximum 6 H4 sessions per day
            session_end = current_time + timedelta(hours=4)
            
            # Last session might be shorter (until market close)
            if i == 5:  # Last session
                next_day_start = session_start + timedelta(days=1)
                session_end = min(session_end, next_day_start)
            
            h4_sessions.append({
                'start': current_time,
                'end': session_end,
                'session_number': i + 1
            })
            
            current_time = session_end
        
        return {
            'pair': pair,
            'date': date,
            'is_forex': is_forex,
            'market_open': market_open_str,
            'session_start': session_start,
            'h4_sessions': h4_sessions,
            'daily_start': session_start,
            'daily_end': session_start + timedelta(days=1)
        }
    
    def find_actual_session_boundaries(self, m1_data: pd.DataFrame, 
                                     ideal_start: datetime, 
                                     ideal_end: datetime) -> Tuple[datetime, datetime]:
        """
        Find actual session boundaries based on available M1 data.
        Handles gaps in data by adjusting to actual data availability.
        
        Args:
            m1_data: M1 DataFrame
            ideal_start: Ideal session start time
            ideal_end: Ideal session end time
            
        Returns:
            Tuple of (actual_start, actual_end)
        """
        if m1_data.empty:
            return ideal_start, ideal_end
        
        # Filter data within ideal session range
        session_mask = (m1_data.index >= ideal_start) & (m1_data.index < ideal_end)
        session_data = m1_data[session_mask]
        
        if session_data.empty:
            return ideal_start, ideal_end
        
        # Use actual first and last available data points
        actual_start = session_data.index[0]
        actual_end = session_data.index[-1] + timedelta(minutes=1)  # Add 1 minute for inclusive end
        
        return actual_start, actual_end
    
    @log_performance
    def convert_to_h4(self, m1_data: pd.DataFrame, pair: str) -> pd.DataFrame:
        """
        Convert M1 data to H4 using session boundaries.
        
        Args:
            m1_data: M1 DataFrame with OHLCV data
            pair: Currency pair symbol
            
        Returns:
            H4 DataFrame
        """
        if m1_data.empty:
            return pd.DataFrame()
        
        h4_candles = []
        
        # Group data by date
        m1_data_by_date = m1_data.groupby(m1_data.index.date)
        
        for date, daily_data in m1_data_by_date:
            date_obj = datetime.combine(date, time.min)
            session_info = self.get_session_boundaries(pair, date_obj)
            
            # Process each H4 session
            for session in session_info['h4_sessions']:
                ideal_start = session['start']
                ideal_end = session['end']
                
                # Find actual boundaries based on available data
                actual_start, actual_end = self.find_actual_session_boundaries(
                    daily_data, ideal_start, ideal_end
                )
                
                # Filter data for this session
                session_mask = (daily_data.index >= actual_start) & (daily_data.index < actual_end)
                session_data = daily_data[session_mask]
                
                if not session_data.empty:
                    # Create H4 candle
                    h4_candle = self._create_ohlcv_candle(session_data, ideal_start)
                    h4_candles.append(h4_candle)
        
        if not h4_candles:
            return pd.DataFrame()
        
        # Create H4 DataFrame
        h4_df = pd.DataFrame(h4_candles)
        h4_df.set_index('timestamp', inplace=True)
        h4_df.sort_index(inplace=True)
        
        return h4_df
    
    @log_performance
    def convert_to_daily(self, m1_data: pd.DataFrame, pair: str) -> pd.DataFrame:
        """
        Convert M1 data to D1 using session boundaries.
        
        Args:
            m1_data: M1 DataFrame with OHLCV data
            pair: Currency pair symbol
            
        Returns:
            D1 DataFrame
        """
        if m1_data.empty:
            return pd.DataFrame()
        
        daily_candles = []
        
        # Group data by date
        m1_data_by_date = m1_data.groupby(m1_data.index.date)
        
        for date, daily_data in m1_data_by_date:
            date_obj = datetime.combine(date, time.min)
            session_info = self.get_session_boundaries(pair, date_obj)
            
            # Use entire day's data for daily candle
            daily_start = session_info['daily_start']
            daily_end = session_info['daily_end']
            
            # Find actual boundaries
            actual_start, actual_end = self.find_actual_session_boundaries(
                daily_data, daily_start, daily_end
            )
            
            # Filter data for this day
            day_mask = (daily_data.index >= actual_start) & (daily_data.index < actual_end)
            day_data = daily_data[day_mask]
            
            if not day_data.empty:
                # Create daily candle
                daily_candle = self._create_ohlcv_candle(day_data, daily_start)
                daily_candles.append(daily_candle)
        
        if not daily_candles:
            return pd.DataFrame()
        
        # Create D1 DataFrame
        d1_df = pd.DataFrame(daily_candles)
        d1_df.set_index('timestamp', inplace=True)
        d1_df.sort_index(inplace=True)
        
        return d1_df
    
    def _create_ohlcv_candle(self, session_data: pd.DataFrame, timestamp: datetime) -> Dict[str, Any]:
        """
        Create OHLCV candle from session data.
        
        Args:
            session_data: M1 data for the session
            timestamp: Candle timestamp
            
        Returns:
            Dictionary with OHLCV data
        """
        if session_data.empty:
            return None
        
        return {
            'timestamp': timestamp,
            'open': session_data['open'].iloc[0],
            'high': session_data['high'].max(),
            'low': session_data['low'].min(),
            'close': session_data['close'].iloc[-1],
            'volume': session_data['volume'].sum() if 'volume' in session_data.columns else 0
        }
    
    @log_performance
    def convert_timeframe(self, m1_data: pd.DataFrame, target_timeframe: str, 
                         pair: str, validate_input: bool = True) -> Optional[pd.DataFrame]:
        """
        Convert M1 data to target timeframe using session-aware logic.
        
        Args:
            m1_data: M1 DataFrame with OHLCV data
            target_timeframe: Target timeframe (M5, H1, H4, D1, etc.)
            pair: Currency pair symbol
            validate_input: Whether to validate input data
            
        Returns:
            Converted DataFrame or None if error
        """
        try:
            if target_timeframe not in self.supported_timeframes:
                logger.error(f"Unsupported timeframe: {target_timeframe}")
                return None
            
            if target_timeframe == 'M1':
                return m1_data.copy()
            
            if validate_input:
                # Check if DataFrame has datetime index or datetime column
                if not (pd.api.types.is_datetime64_any_dtype(m1_data.index) or
                       ('datetime' in m1_data.columns and pd.api.types.is_datetime64_any_dtype(m1_data['datetime']))):
                    logger.error("DataFrame must have datetime index or datetime column")
                    return None

                # Check required OHLCV columns
                required_cols = ['open', 'high', 'low', 'close']
                missing_cols = set(required_cols) - set(m1_data.columns)
                if missing_cols:
                    logger.error(f"Missing required columns: {missing_cols}")
                    return None
            
            if m1_data.empty:
                logger.warning("Empty input DataFrame")
                return pd.DataFrame()
            
            # Cache key for this conversion
            cache_key = f"{pair}_{target_timeframe}_{len(m1_data)}_{m1_data.index[0]}_{m1_data.index[-1]}"
            
            with self._lock:
                if cache_key in self.cache:
                    logger.debug(f"Using cached conversion for {cache_key}")
                    return self.cache[cache_key].copy()
            
            # Perform conversion based on timeframe
            if target_timeframe == 'H4':
                result = self.convert_to_h4(m1_data, pair)
            elif target_timeframe == 'D1':
                result = self.convert_to_daily(m1_data, pair)
            else:
                # For other timeframes, use pandas resampling with session awareness
                result = self._convert_with_resampling(m1_data, target_timeframe, pair)
            
            # Cache the result
            with self._lock:
                self.cache[cache_key] = result.copy()
                
                # Limit cache size
                if len(self.cache) > 100:
                    # Remove oldest entries
                    oldest_keys = list(self.cache.keys())[:20]
                    for key in oldest_keys:
                        del self.cache[key]
            
            logger.info(f"Converted {len(m1_data)} M1 candles to {len(result)} {target_timeframe} candles for {pair}")
            return result
            
        except Exception as e:
            logger.error(f"Error converting to {target_timeframe}: {e}")
            return None
    
    def _convert_with_resampling(self, m1_data: pd.DataFrame, target_timeframe: str, pair: str) -> pd.DataFrame:
        """
        Convert using pandas resampling for standard timeframes.
        
        Args:
            m1_data: M1 DataFrame
            target_timeframe: Target timeframe
            pair: Currency pair symbol
            
        Returns:
            Converted DataFrame
        """
        # Frequency mapping
        freq_mapping = {
            'M5': '5min',
            'M15': '15min',
            'M30': '30min',
            'H1': '1h',
            'W1': '1W',
            'MN1': '1M'
        }
        
        if target_timeframe not in freq_mapping:
            logger.error(f"No resampling mapping for {target_timeframe}")
            return pd.DataFrame()
        
        freq = freq_mapping[target_timeframe]
        
        # Resample using OHLC logic
        resampled = m1_data.resample(freq).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        return resampled
    
    def clear_cache(self):
        """Clear conversion cache."""
        with self._lock:
            self.cache.clear()
            logger.info("Session-aware converter cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information."""
        with self._lock:
            return {
                'cache_size': len(self.cache),
                'cache_keys': list(self.cache.keys())
            }
