"""
Data management module for Lionaire platform.
"""

from .data_manager import <PERSON>Manager
from .data_loader import <PERSON>Loader
from .timeframe_converter import TimeframeConverter

# Keep original imports working for backward compatibility
# Phase 5.1 components available but not auto-imported to avoid issues
try:
    from .enhanced_data_manager import EnhancedDataManager
    from .session_aware_converter import SessionAwareConverter
    _phase5_available = True
except ImportError:
    _phase5_available = False

# Global instances for backward compatibility
data_manager = DataManager()

__all__ = [
    'DataManager',
    'DataLoader',
    'TimeframeConverter',
    'data_manager'
]

# Add Phase 5.1 components to __all__ if available
if _phase5_available:
    __all__.extend(['EnhancedDataManager', 'SessionAwareConverter'])
