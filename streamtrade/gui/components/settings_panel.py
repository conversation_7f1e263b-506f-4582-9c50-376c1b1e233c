"""
Settings Panel Component for Lionaire Platform.
Displays and manages Phase 5.1 user settings.
"""

import streamlit as st
import json
from typing import Dict, Any

from ...config.strings import get_string


class SettingsPanel:
    """
    Settings management panel for Phase 5.1 configuration.
    """
    
    def __init__(self):
        pass
    
    def render(self):
        """Render the complete settings panel."""
        st.subheader("⚙️ Platform Settings")
        
        # Create tabs for different setting categories
        tab1, tab2, tab3, tab4 = st.tabs([
            "🌍 Timezone & Sessions", 
            "📊 Data Loading", 
            "💾 Cache Settings",
            "🎨 UI Preferences"
        ])
        
        with tab1:
            self._render_timezone_settings()
        
        with tab2:
            self._render_data_loading_settings()
        
        with tab3:
            self._render_cache_settings()
        
        with tab4:
            self._render_ui_preferences()
    
    def _render_timezone_settings(self):
        """Render timezone and market session settings."""
        st.write("**Timezone Configuration**")

        # Use simple default values to avoid import issues
        try:
            # Data timezone
            data_tz = st.selectbox(
                "Data Timezone",
                options=["UTC-5", "UTC-4", "UTC-3", "UTC-2", "UTC-1", "UTC", "UTC+1", "UTC+2"],
                index=0,  # Default UTC-5
                help="Timezone of the historical data (histdata.com uses EST = UTC-5)"
            )
            
            # Display timezone
            display_tz = st.selectbox(
                "Display Timezone", 
                options=["UTC+7", "UTC+8", "UTC+9", "UTC", "UTC-5", "UTC-4"],
                index=0,  # Default UTC+7 (Asia/Jakarta)
                help="Timezone for displaying times in the interface"
            )
            
            st.write("**Market Session Configuration**")
            
            # Forex market open
            forex_open = st.time_input(
                "Forex Market Open",
                value=st.session_state.get('forex_open_time', None) or st.time(16, 0),
                help="Daily market open time for Forex pairs"
            )
            
            # Non-forex market open
            non_forex_open = st.time_input(
                "Non-Forex Market Open",
                value=st.session_state.get('non_forex_open_time', None) or st.time(17, 0),
                help="Daily market open time for Gold, Indices, etc."
            )
            
            # Non-forex symbols
            st.write("**Non-Forex Symbols**")
            non_forex_symbols = st.multiselect(
                "Select Non-Forex Instruments",
                options=["XAUUSD", "SPXUSD", "NSXUSD", "BTCUSD", "ETHUSD"],
                default=["XAUUSD", "SPXUSD", "NSXUSD"],
                help="Instruments that use Non-Forex market session times"
            )
            
            # Display current session boundaries
            st.write("**Session Boundaries Preview**")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**Forex (EURUSD)**")
                forex_time = forex_open.strftime("%H:%M")
                forex_sessions = self._calculate_session_preview(forex_time)
                for i, session in enumerate(forex_sessions, 1):
                    st.write(f"H4-{i}: {session}")
            
            with col2:
                st.write("**Non-Forex (XAUUSD)**")
                non_forex_time = non_forex_open.strftime("%H:%M")
                non_forex_sessions = self._calculate_session_preview(non_forex_time)
                for i, session in enumerate(non_forex_sessions, 1):
                    st.write(f"H4-{i}: {session}")
            
            # Save button
            if st.button("💾 Save Timezone Settings"):
                # Save settings to session state for now
                st.session_state.update({
                    'data_timezone': data_tz,
                    'display_timezone': display_tz,
                    'forex_open_time': forex_open,
                    'non_forex_open_time': non_forex_open,
                    'non_forex_symbols': non_forex_symbols
                })

                # Try to save to file
                try:
                    self._save_settings_to_file({
                        'timezone': {
                            'data_timezone': data_tz,
                            'display_timezone': display_tz
                        },
                        'market_sessions': {
                            'forex_open': forex_open.strftime("%H:%M"),
                            'non_forex_open': non_forex_open.strftime("%H:%M"),
                            'non_forex_symbols': non_forex_symbols
                        }
                    })
                    st.success("✅ Timezone settings saved to file!")
                except Exception as e:
                    st.warning(f"Settings saved to session but file save failed: {e}")
                    st.success("✅ Timezone settings saved to session!")

        except Exception as e:
            st.error(f"Error in timezone settings: {e}")
            st.info("Using default values. Phase 5.1 settings system may need initialization.")
    
    def _render_data_loading_settings(self):
        """Render data loading configuration."""
        st.write("**Data Loading Strategy**")
        
        # N Days Back setting
        days_back = st.number_input(
            "Default Days Back",
            min_value=1,
            max_value=30,
            value=5,
            help="Default number of days to load when opening a chart"
        )
        
        # Max candles settings
        col1, col2 = st.columns(2)
        
        with col1:
            max_load = st.number_input(
                "Max Candles to Load",
                min_value=10000,
                max_value=1000000,
                value=200000,
                step=10000,
                help="Maximum candles to load from files"
            )
        
        with col2:
            max_display = st.number_input(
                "Max Candles to Display",
                min_value=1000,
                max_value=50000,
                value=15000,
                step=1000,
                help="Maximum candles to show on chart"
            )
        
        # Enabled timeframes
        st.write("**Enabled Timeframes**")
        
        all_timeframes = ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"]
        default_enabled = ["M1", "M5", "M15", "H1", "H4", "D1"]
        
        enabled_tfs = st.multiselect(
            "Select Enabled Timeframes",
            options=all_timeframes,
            default=default_enabled,
            help="Timeframes available in the interface"
        )
        
        # Cache all TF option
        cache_all_tf = st.checkbox(
            "Cache All Timeframes on Load",
            value=False,
            help="Pre-cache all enabled timeframes when loading data (uses more memory)"
        )
        
        # Insufficient data behavior
        st.write("**Insufficient Data Handling**")
        insufficient_behavior = st.radio(
            "When insufficient data is available:",
            options=["Show warning message", "Load automatically in background"],
            index=0,
            help="How to handle cases where requested data range is not available"
        )
        
        # Save button
        if st.button("💾 Save Data Loading Settings"):
            # Save to session state
            st.session_state.update({
                'days_back_default': days_back,
                'max_candles_load': max_load,
                'max_candles_display': max_display,
                'enabled_timeframes': enabled_tfs,
                'cache_all_tf_on_load': cache_all_tf,
                'insufficient_data_behavior': insufficient_behavior
            })

            # Try to save to file
            try:
                self._save_settings_to_file({
                    'data_loading': {
                        'days_back_default': days_back,
                        'max_candles_load': max_load,
                        'max_candles_display': max_display,
                        'enabled_timeframes': enabled_tfs,
                        'cache_all_tf_on_load': cache_all_tf,
                        'insufficient_data_behavior': insufficient_behavior
                    }
                })
                st.success("✅ Data loading settings saved to file!")
            except Exception as e:
                st.warning(f"Settings saved to session but file save failed: {e}")
                st.success("✅ Data loading settings saved to session!")
    
    def _render_cache_settings(self):
        """Render cache configuration."""
        st.write("**Cache Configuration**")
        
        # Max cache size
        cache_size_gb = st.slider(
            "Maximum Cache Size (GB)",
            min_value=1,
            max_value=50,
            value=10,
            help="Maximum disk space for cache storage"
        )
        
        # Cache compression
        compression = st.selectbox(
            "Cache Compression",
            options=["snappy", "gzip", "lz4", "none"],
            index=0,
            help="Compression method for cached data files"
        )
        
        # Enable disk cache
        enable_disk_cache = st.checkbox(
            "Enable Disk Cache",
            value=True,
            help="Store cache on disk for persistence across sessions"
        )
        
        # Cache statistics (if available)
        st.write("**Cache Statistics**")
        
        try:
            from ...data import data_manager
            cache_stats = data_manager.get_cache_stats()
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Cache Entries", cache_stats.get('entries', 0))
            
            with col2:
                st.metric("Cache Size (MB)", f"{cache_stats.get('total_size_mb', 0):.1f}")
            
            with col3:
                st.metric("Cache Usage %", f"{cache_stats.get('usage_percent', 0):.1f}%")
            
            # Clear cache button
            if st.button("🗑️ Clear Cache"):
                data_manager.clear_cache()
                st.success("✅ Cache cleared!")
                st.rerun()
                
        except Exception as e:
            st.info("Cache statistics not available")
        
        # Save button
        if st.button("💾 Save Cache Settings"):
            # Save to session state
            st.session_state.update({
                'max_cache_size_gb': cache_size_gb,
                'cache_compression': compression,
                'enable_disk_cache': enable_disk_cache
            })

            # Try to save to file
            try:
                self._save_settings_to_file({
                    'cache': {
                        'max_cache_size_gb': cache_size_gb,
                        'cache_compression': compression,
                        'enable_disk_cache': enable_disk_cache
                    }
                })
                st.success("✅ Cache settings saved to file!")
            except Exception as e:
                st.warning(f"Settings saved to session but file save failed: {e}")
                st.success("✅ Cache settings saved to session!")
    
    def _render_ui_preferences(self):
        """Render UI preferences."""
        st.write("**Chart Preferences**")
        
        # Default timeframe
        default_tf = st.selectbox(
            "Default Timeframe",
            options=["M1", "M5", "M15", "M30", "H1", "H4", "D1"],
            index=4,  # H1
            help="Default timeframe when opening charts"
        )
        
        # Chart style
        chart_style = st.selectbox(
            "Default Chart Style",
            options=["candlestick", "ohlc", "line"],
            index=0,
            help="Default chart visualization style"
        )
        
        # Remove gaps
        remove_gaps = st.checkbox(
            "Remove Weekend Gaps",
            value=True,
            help="Remove gaps in chart for better visualization"
        )
        
        # Crosshair settings
        st.write("**Crosshair Settings**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            crosshair_vertical = st.checkbox(
                "Vertical Crosshair",
                value=True,
                help="Show vertical crosshair line"
            )
        
        with col2:
            crosshair_horizontal = st.checkbox(
                "Horizontal Crosshair", 
                value=True,
                help="Show horizontal crosshair line with price level"
            )
        
        # Save button
        if st.button("💾 Save UI Preferences"):
            # Save to session state
            st.session_state.update({
                'default_timeframe': default_tf,
                'chart_style': chart_style,
                'remove_gaps': remove_gaps,
                'crosshair_vertical': crosshair_vertical,
                'crosshair_horizontal': crosshair_horizontal
            })

            # Try to save to file
            try:
                self._save_settings_to_file({
                    'ui_preferences': {
                        'default_timeframe': default_tf,
                        'chart_style': chart_style,
                        'remove_gaps': remove_gaps,
                        'crosshair_vertical': crosshair_vertical,
                        'crosshair_horizontal': crosshair_horizontal
                    }
                })
                st.success("✅ UI preferences saved to file!")
            except Exception as e:
                st.warning(f"Settings saved to session but file save failed: {e}")
                st.success("✅ UI preferences saved to session!")
    
    def _calculate_session_preview(self, market_open_str: str) -> list:
        """Calculate H4 session boundaries for preview."""
        try:
            hour, minute = map(int, market_open_str.split(':'))
            sessions = []
            
            for i in range(6):
                start_hour = (hour + i * 4) % 24
                end_hour = (hour + (i + 1) * 4) % 24
                
                if end_hour == hour:  # Last session
                    sessions.append(f"{start_hour:02d}:{minute:02d} → Close")
                else:
                    sessions.append(f"{start_hour:02d}:{minute:02d} → {end_hour:02d}:{minute:02d}")
            
            return sessions
            
        except Exception:
            return ["Error calculating sessions"]
    
    def render_phase5_status(self):
        """Render Phase 5.1 implementation status."""
        st.subheader("🚀 Phase 5.1 Status")
        
        # Implementation status
        st.write("**Implementation Status**")
        
        status_items = [
            ("User Settings System", True, "Project-local JSON configuration"),
            ("Timezone Configuration", True, "Data/Display timezone support"),
            ("Market Sessions", True, "Forex vs Non-Forex differentiation"),
            ("Session-Aware Conversion", True, "Proper market boundaries"),
            ("N Days Back Loading", True, "Time-based data loading"),
            ("Enhanced Data Manager", True, "Intelligent caching system"),
            ("Settings UI", True, "This settings panel")
        ]
        
        for item, status, description in status_items:
            col1, col2, col3 = st.columns([3, 1, 4])
            
            with col1:
                st.write(f"**{item}**")
            
            with col2:
                if status:
                    st.success("✅")
                else:
                    st.error("❌")
            
            with col3:
                st.write(description)
        
        # Next steps
        st.write("**Next Steps**")
        st.info("🎯 **Phase 5.2**: Smart Disk Cache System with Parquet files and LRU eviction")
        st.info("🎯 **Phase 5.3**: Advanced Indicator Cache with per-indicator caching")
        
        # Documentation link
        st.write("**Documentation**")
        st.write("📖 See `/streamtrade/docs/012-phase5-1-implementation.md` for detailed documentation")
        st.write("📋 See `/streamtrade/docs/013-phase5-1-completion-summary.md` for completion summary")

    def _save_settings_to_file(self, settings_data: Dict[str, Any]):
        """Save settings to JSON file."""
        import json
        from pathlib import Path
        from datetime import datetime

        # Get project directory
        project_dir = Path(__file__).parent.parent.parent
        config_dir = project_dir / 'config'
        config_dir.mkdir(exist_ok=True)

        settings_file = config_dir / 'user_settings.json'

        # Load existing settings or create new
        if settings_file.exists():
            with open(settings_file, 'r', encoding='utf-8') as f:
                existing_settings = json.load(f)
        else:
            existing_settings = {
                'version': '1.0.0',
                'created': datetime.now().isoformat()
            }

        # Merge new settings
        for key, value in settings_data.items():
            if key in existing_settings and isinstance(existing_settings[key], dict) and isinstance(value, dict):
                existing_settings[key].update(value)
            else:
                existing_settings[key] = value

        # Update timestamp
        existing_settings['last_updated'] = datetime.now().isoformat()

        # Save to file
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(existing_settings, f, indent=2, ensure_ascii=False)

        return settings_file
